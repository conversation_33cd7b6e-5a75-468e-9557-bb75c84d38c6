<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            text-align: center;
            color: white;
        }

        .animated-text {
            font-size: 4rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            min-height: 1.2em;
            display: inline-block;
        }

        .status {
            margin-top: 20px;
            font-size: 1.2rem;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>AI Designer Animation Test</h1>
        <div class="animated-text" id="animatedText">AI Designer</div>
        <div class="status" id="status">Animation: Ready</div>
        <button onclick="testAnimation()" style="margin-top: 20px; padding: 10px 20px; font-size: 1rem;">Test
            Animation</button>
    </div>

    <script>
        let displayText = '';
        let animationType = 0;
        const fullText = 'AI Designer';
        const animationTypes = ['typewriter', 'fadeIn', 'slideIn', 'glitch', 'wave'];
        let activeTimer = null;

        function updateDisplay(text) {
            document.getElementById('animatedText').textContent = text || fullText;
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = `Animation: ${status}`;
        }

        function typewriterAnimation() {
            let currentIndex = 0;
            updateStatus('Typewriter');
            const timer = setInterval(() => {
                if (currentIndex <= fullText.length) {
                    const textSlice = fullText.slice(0, currentIndex);
                    updateDisplay(textSlice);
                    currentIndex++;
                } else {
                    updateDisplay(fullText);
                    clearInterval(timer);
                    setTimeout(() => {
                        updateDisplay('');
                        animationType = (animationType + 1) % animationTypes.length;
                        runAnimation();
                    }, 2000);
                }
            }, 120);
            return timer;
        }

        function fadeInAnimation() {
            const letters = fullText.split('');
            let currentIndex = 0;
            updateStatus('Fade In');
            updateDisplay('');

            const timer = setInterval(() => {
                if (currentIndex < letters.length) {
                    const currentText = letters.slice(0, currentIndex + 1).join('');
                    updateDisplay(currentText);
                    currentIndex++;
                } else {
                    updateDisplay(fullText);
                    clearInterval(timer);
                    setTimeout(() => {
                        updateDisplay('');
                        animationType = (animationType + 1) % animationTypes.length;
                        runAnimation();
                    }, 2000);
                }
            }, 100);
            return timer;
        }

        function slideInAnimation() {
            const words = fullText.split(' ');
            let currentIndex = 0;
            updateStatus('Slide In');
            updateDisplay('');

            const timer = setInterval(() => {
                if (currentIndex < words.length) {
                    const currentWords = words.slice(0, currentIndex + 1);
                    updateDisplay(currentWords.join(' '));
                    currentIndex++;
                } else {
                    updateDisplay(fullText);
                    clearInterval(timer);
                    setTimeout(() => {
                        updateDisplay('');
                        animationType = (animationType + 1) % animationTypes.length;
                        runAnimation();
                    }, 2000);
                }
            }, 300);
            return timer;
        }

        function glitchAnimation() {
            // Use safer glitch characters that don't look like code - no < > symbols
            const glitchChars = '█▓▒░▄▀■□▪▫●○◆◇★☆※';
            let phase = 0;
            updateStatus('Glitch');

            const timer = setInterval(() => {
                if (phase < 6) {
                    let glitched = '';
                    for (let i = 0; i < fullText.length; i++) {
                        glitched += Math.random() > 0.85 ? glitchChars[Math.floor(Math.random() * glitchChars.length)] : fullText[i];
                    }
                    updateDisplay(glitched);
                    phase++;
                } else if (phase < 9) {
                    let transitioning = '';
                    for (let i = 0; i < fullText.length; i++) {
                        const glitchProbability = Math.max(0, 0.85 - (phase - 6) * 0.25);
                        transitioning += Math.random() > glitchProbability ? fullText[i] : glitchChars[Math.floor(Math.random() * glitchChars.length)];
                    }
                    updateDisplay(transitioning);
                    phase++;
                } else {
                    updateDisplay(fullText);
                    clearInterval(timer);
                    setTimeout(() => {
                        updateDisplay('');
                        animationType = (animationType + 1) % animationTypes.length;
                        runAnimation();
                    }, 2000);
                }
            }, 80);
            return timer;
        }

        function waveAnimation() {
            let currentIndex = 0;
            updateStatus('Wave');
            const timer = setInterval(() => {
                if (currentIndex <= fullText.length) {
                    let waveText = '';
                    for (let i = 0; i < currentIndex; i++) {
                        const char = fullText[i];
                        if (char && typeof char === 'string') {
                            const wave = Math.sin((Date.now() / 200) + i) > 0;
                            waveText += wave ? char.toUpperCase() : char.toLowerCase();
                        }
                    }
                    updateDisplay(waveText);
                    currentIndex++;
                } else {
                    updateDisplay(fullText);
                    clearInterval(timer);
                    setTimeout(() => {
                        updateDisplay('');
                        animationType = (animationType + 1) % animationTypes.length;
                        runAnimation();
                    }, 2000);
                }
            }, 150);
            return timer;
        }

        function runAnimation() {
            if (activeTimer) {
                clearInterval(activeTimer);
            }

            const currentAnimation = animationTypes[animationType];

            switch (currentAnimation) {
                case 'typewriter':
                    activeTimer = typewriterAnimation();
                    break;
                case 'fadeIn':
                    activeTimer = fadeInAnimation();
                    break;
                case 'slideIn':
                    activeTimer = slideInAnimation();
                    break;
                case 'glitch':
                    activeTimer = glitchAnimation();
                    break;
                case 'wave':
                    activeTimer = waveAnimation();
                    break;
                default:
                    activeTimer = typewriterAnimation();
            }
        }

        function testAnimation() {
            animationType = 0;
            runAnimation();
        }

        // Auto-start animation
        setTimeout(() => {
            testAnimation();
        }, 1000);
    </script>
</body>

</html>