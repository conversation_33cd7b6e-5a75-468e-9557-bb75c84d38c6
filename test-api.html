<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Media Expert API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Social Media Expert API Tester</h1>
        <p>Test the AI Social Media Expert System directly through the API endpoints.</p>
        
        <div class="form-group">
            <label>API Base URL:</label>
            <input type="text" id="baseUrl" value="http://localhost:3001/api/social-media-expert" style="width: 300px;">
        </div>
        
        <div class="form-group">
            <label>Business Profile:</label>
            <select id="profileSelect">
                <option value="samaki">Samaki Cookies (Restaurant)</option>
                <option value="custom">Custom Profile</option>
            </select>
        </div>
    </div>

    <!-- Test 1: Business Analysis -->
    <div class="container test-section">
        <h2>📊 Test 1: Business Analysis</h2>
        <p>Analyze a business and generate insights, strategy, and recommendations.</p>
        <button onclick="testBusinessAnalysis()">Run Business Analysis</button>
        <div id="analysisResult" class="result" style="display: none;"></div>
    </div>

    <!-- Test 2: Generate Posts -->
    <div class="container test-section">
        <h2>📝 Test 2: Generate Social Media Posts</h2>
        <div class="form-group">
            <label>Platform:</label>
            <select id="platformSelect">
                <option value="Instagram">Instagram</option>
                <option value="Facebook">Facebook</option>
                <option value="LinkedIn">LinkedIn</option>
                <option value="Twitter">Twitter</option>
                <option value="TikTok">TikTok</option>
            </select>
        </div>
        <div class="form-group">
            <label>Post Count:</label>
            <input type="number" id="postCount" value="3" min="1" max="10">
        </div>
        <button onclick="testGeneratePosts()">Generate Posts</button>
        <div id="postsResult" class="result" style="display: none;"></div>
    </div>

    <!-- Test 3: Complete Package -->
    <div class="container test-section">
        <h2>🎯 Test 3: Complete Package</h2>
        <p>Get a complete social media package including analysis, strategy, posts, and calendar.</p>
        <button onclick="testCompletePackage()">Get Complete Package</button>
        <div id="packageResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // Sample business profiles
        const businessProfiles = {
            samaki: {
                businessName: 'Samaki Cookies',
                businessType: 'restaurant',
                industry: 'Food & Beverage',
                location: 'Nairobi',
                city: 'Nairobi',
                country: 'Kenya',
                description: 'Artisanal cookie bakery specializing in nutritious, locally-sourced ingredients',
                mission: 'To provide healthy, delicious cookies while fighting malnutrition in Kenya',
                vision: 'To be the leading provider of nutritious baked goods in East Africa',
                founded: '2023',
                employeeCount: 15,
                targetAudience: ['Families', 'Health-conscious individuals', 'Children', 'Local businesses'],
                ageGroups: ['Children', 'Young adults', 'Families', 'Seniors'],
                interests: ['Healthy eating', 'Local food', 'Sustainability', 'Community health'],
                lifestyle: ['Health-conscious', 'Community-minded', 'Quality-focused'],
                services: ['Artisanal cookies', 'Custom orders', 'Corporate catering', 'Wholesale distribution'],
                products: ['Nutritious cookies', 'Gluten-free options', 'Seasonal specialties'],
                specialties: ['Malnutrition-fighting cookies', 'Local ingredient sourcing', 'Community health programs'],
                uniqueValue: 'Cookies that taste great AND fight malnutrition',
                competitiveAdvantages: ['Local ingredient sourcing', 'Health-focused recipes', 'Community impact'],
                brandColors: ['#8B4513', '#228B22', '#FFD700'],
                primaryColor: '#8B4513',
                accentColor: '#228B22',
                backgroundColor: '#FFFFFF',
                visualStyle: 'rustic',
                brandVoice: 'friendly',
                brandPersonality: ['Caring', 'Authentic', 'Community-focused', 'Health-conscious'],
                contentThemes: ['Health & nutrition', 'Local community', 'Sustainable business', 'Family values'],
                contentTone: 'educational',
                preferredPostTypes: ['Behind-the-scenes', 'Customer stories', 'Health tips', 'Community events'],
                contentFrequency: 'daily',
                platforms: ['Instagram', 'Facebook', 'LinkedIn'],
                primaryPlatform: 'Instagram',
                socialMediaGoals: ['Brand awareness', 'Community building', 'Education', 'Sales'],
                targetMetrics: ['Engagement', 'Followers', 'Website traffic', 'Conversions'],
                localCulture: ['Kenyan hospitality', 'Community values', 'Traditional food culture'],
                communityInvolvement: ['Local health programs', 'School partnerships', 'Community events'],
                localEvents: ['Nairobi Food Festival', 'Community Health Day', 'Local Markets'],
                seasonalContent: ['Rainy season comfort foods', 'Dry season fresh ingredients', 'Holiday specials'],
                localTrends: ['Health consciousness', 'Local sourcing', 'Community support'],
                competitors: ['International bakeries', 'Local supermarkets', 'Other bakeries'],
                marketPosition: 'Premium health-focused local bakery',
                pricingStrategy: 'Premium pricing for quality and health benefits',
                customerFeedback: ['Love the health focus', 'Great taste', 'Community impact'],
                challenges: ['Cost of local ingredients', 'Education about nutrition', 'Competition from cheaper options'],
                opportunities: ['School partnerships', 'Corporate wellness programs', 'Tourism market'],
                website: 'https://samakicookies.ke',
                phone: '+254-700-123-456',
                email: '<EMAIL>',
                address: '123 Uhuru Highway, Nairobi, Kenya',
                socialMediaHandles: {
                    instagram: '@samakicookies',
                    facebook: 'SamakiCookiesNairobi',
                    linkedin: 'samaki-cookies-ltd'
                }
            }
        };

        function getCurrentProfile() {
            const profileType = document.getElementById('profileSelect').value;
            return businessProfiles[profileType];
        }

        function getBaseUrl() {
            return document.getElementById('baseUrl').value;
        }

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function makeApiCall(action, additionalData = {}) {
            const baseUrl = getBaseUrl();
            const businessProfile = getCurrentProfile();
            
            const requestBody = {
                action: action,
                businessProfile: businessProfile,
                ...additionalData
            };

            try {
                const response = await fetch(baseUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.error || `HTTP ${response.status}`);
                }
                
                return result;
            } catch (error) {
                throw new Error(`API call failed: ${error.message}`);
            }
        }

        async function testBusinessAnalysis() {
            try {
                const result = await makeApiCall('analyze');
                showResult('analysisResult', result);
            } catch (error) {
                showResult('analysisResult', { error: error.message }, true);
            }
        }

        async function testGeneratePosts() {
            try {
                const platform = document.getElementById('platformSelect').value;
                const count = parseInt(document.getElementById('postCount').value);
                
                const result = await makeApiCall('generate-posts', {
                    platform: platform,
                    count: count
                });
                
                showResult('postsResult', result);
            } catch (error) {
                showResult('postsResult', { error: error.message }, true);
            }
        }

        async function testCompletePackage() {
            try {
                const platform = document.getElementById('platformSelect').value;
                const count = parseInt(document.getElementById('postCount').value);
                
                const result = await makeApiCall('complete-package', {
                    platform: platform,
                    count: count
                });
                
                showResult('packageResult', result);
            } catch (error) {
                showResult('packageResult', { error: error.message }, true);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Social Media Expert API Tester loaded!');
            console.log('Make sure your Next.js server is running on port 3001');
        });
    </script>
</body>
</html>
