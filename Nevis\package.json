{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "dev-webpack": "next dev -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@genkit-ai/googleai": "^1.14.1", "@genkit-ai/next": "^1.14.1", "@google/genai": "^1.16.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/stripe-js": "^7.9.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/mongodb": "^4.0.6", "@types/xml2js": "^0.4.14", "antd": "^5.27.1", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "genkit": "^1.14.1", "html-to-image": "^1.11.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "micro": "^10.0.1", "mongodb": "^6.19.0", "mongoose": "^8.18.0", "next": "15.3.3", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "openai": "^5.12.2", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-wrap-balancer": "^1.1.1", "recharts": "^2.15.1", "sharp": "^0.34.3", "stripe": "^18.4.0", "sweetalert2": "^11.22.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "wav": "^1.0.2", "xml2js": "^0.6.2", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/wav": "^1.0.3", "genkit-cli": "^1.14.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}