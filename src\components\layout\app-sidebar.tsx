// src/components/layout/app-sidebar.tsx
"use client";

import React from "react";
import Link from "next/link";
import {
  Bo<PERSON>,
  CalendarDays,
  Sparkles,
  Link as LinkIcon,
  Wand,
  Settings,
  Zap,
  Calendar,
  Archive,
  LayoutDashboard,
  Building2,
  Palette,
  Coins,
  LogOut,
  User,
} from "lucide-react";
import { UnifiedBrandSelector } from '@/components/brand/unified-brand-selector';
import { usePathname } from "next/navigation";
import { CreditsIndicator } from "@/components/pricing/CreditsDisplay";
import { useAuth } from "@/hooks/use-auth-supabase";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

export function AppSidebar() {
  const pathname = usePathname();
  const { user, signOut } = useAuth();
  const isActive = (path: string) => pathname.startsWith(path);
  
  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <Link href="/" className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-xl font-bold text-primary-foreground font-headline">
            Crevo
          </h1>
        </Link>

        {/* Unified Brand Selector */}
        <div className="px-2 py-2">
          <UnifiedBrandSelector />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/dashboard")}
              tooltip="Dashboard"
            >
              <Link href="/dashboard">
                <LayoutDashboard />
                <span>Dashboard</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/brands")}
              tooltip="Manage Brands"
            >
              <Link href="/brands">
                <Building2 />
                <span>Manage Brands</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/brand-profile")}
              tooltip="Brand Profile"
            >
              <Link href="/brand-profile">
                <Sparkles />
                <span>Brand Profile</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/quick-content")}
              tooltip="Quick Content"
            >
              <Link href="/quick-content">
                <Zap />
                <span>Quick Content</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/creative-studio")}
              tooltip="Creative Studio"
            >
              <Link href="/creative-studio">
                <Palette />
                <span>Creative Studio</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/content-calendar")}
              tooltip="Content Calendar"
            >
              <Link href="/content-calendar">
                <Calendar />
                <span>Content Calendar</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/artifacts-brand-scoped")}
              tooltip="Artifacts - Coming Soon"
              disabled
            >
              <Link href="/artifacts-brand-scoped">
                <Archive />
                <span>Artifacts</span>
                <span className="ml-auto text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">Coming Soon</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isActive("/social-connect")}
              tooltip="Social Media Connect - Coming Soon"
              disabled
            >
              <Link href="/social-connect">
                <LinkIcon />
                <span>Social Media Connect</span>
                <span className="ml-auto text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">Coming Soon</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>


        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="px-3 py-2 text-sm text-muted-foreground">
              {user?.email && (
                <div className="flex items-center gap-2 mb-2">
                  <User className="w-4 h-4" />
                  <span className="truncate">{user.email}</span>
                </div>
              )}
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 text-red-600 hover:text-red-800 transition-colors w-full"
              >
                <LogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </button>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
