# Image Generation Documentation

This document explains the process of how LocalBuzz generates brand-consistent and visually appealing designs for your social media posts.

## Overview

The image generation is handled by a sophisticated AI flow that takes multiple inputs about your brand and the desired post content to create a unique and relevant image. The core of this process is a Genkit flow located in `src/ai/flows/generate-brand-consistent-image.ts`.

## The Process: From Input to Image

The generation process can be broken down into four main steps:

### 1. Data Collection

The system starts by collecting crucial pieces of information from your brand profile and the content generation request:

- **Business Type:** The industry you operate in (e.g., "Restaurant", "Salon").
- **Location:** The city and state of your business.
- **Visual Style:** The aesthetic defined during the brand analysis (e.g., "modern", "vintage", "minimalist").
- **Logo Data URL:** The exact logo file you uploaded. This is passed as a Base64 encoded data URI to ensure pixel-perfect usage.
- **Image Text:** A short, catchy headline (max 5 words) generated by the `generateDailyPost` flow, intended to be placed on the image.

### 2. The AI Model

We use Google's `gemini-2.5-flash-image-preview` model. This is a powerful multimodal AI that can understand and process both text instructions and image inputs (your logo) simultaneously.

### 3. The Prompting Strategy

This is the most critical step. We send a carefully constructed, multi-part prompt to the AI. This isn't just a single sentence; it's a series of clear, sequential instructions that guide the AI's creative process.

The prompt instructs the AI to think in three stages:

1.  **Generate the Background:** `Generate an appealing background image for a social media post for a {businessType} in {location}. The brand's visual style is {visualStyle}. The image should have a clear, uncluttered area suitable for placing text.`
    - This command tells the AI to create a high-quality, relevant background first, and importantly, to intentionally leave a clean space for the text to be added later.

2.  **Overlay the Text:** `Then, overlay the following text onto the image: "{imageText}". It is critical that the text is clearly readable, well-composed, and not cut off or truncated at the edges of the image. The entire text must be visible.`
    - This command explicitly focuses on the typography. It tells the AI to place the provided text onto the background it just created, with strict rules about readability, composition, and ensuring no words are cut off.

3.  **Place the Logo:** `Finally, place the provided logo naturally onto the generated background image. The logo should be clearly visible but not overpower the main subject. It could be on a product, a sign, or as a subtle watermark.`
    - This final command treats your uploaded logo as a separate asset to be placed onto the composite image. It encourages a natural integration rather than just stamping it on top.

### 4. Output

The AI model processes these instructions and returns a single, composite image in a `data:image/png;base64,...` format. This image is then passed back to the application and displayed in your Content Calendar.

By breaking the process down into these distinct, logical steps, we guide the AI to produce higher-quality, more reliable, and more aesthetically pleasing results that meet all the specified criteria.
